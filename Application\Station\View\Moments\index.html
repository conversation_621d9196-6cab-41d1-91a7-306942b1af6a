<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>朋友圈素材</title>
    <link rel="stylesheet" href="/static/stations/css/index.css">
    <link rel="stylesheet" href="/static/stations/font/iconfont.css">
    <style>
        body {
            background-color: #ededed;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: #333;
        }
        .nav-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid #eee;
            z-index: 100;
            display: none; /* 初始隐藏，使用更简单的方式 */
        }
        .nav-title {
            font-size: 17px;
            font-weight: bold;
            color: #000;
            flex: 1;
            text-align: center;
        }
        .nav-left {
            width: 40px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .nav-right {
            width: 40px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .back-icon {
            font-size: 20px;
            color: #000;
        }
        .camera-icon {
            font-size: 20px;
            color: #000;
        }
        .content-wrapper {
            margin-top: 0;
        }
        .company-header {
            background-color: #222;
            color: white;
            padding: 15px;
            text-align: right;
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }
        .company-header .logo-text {
            font-size: 18px;
            font-weight: bold;
            margin-right: 10px;
        }
        .company-header .logo-img {
            width: 40px;
            height: 40px;
            border: 1px solid white;
            padding: 2px;
        }
        .moment-item {
            background-color: #fff;
            margin: 10px 0;
            padding: 15px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .moment-item .header {
            background-color: transparent;
            display: flex;
            align-items: center;
            padding: 0;
            margin-bottom: 10px;
            justify-content: flex-start;
        }
        .moment-item .avatar {
            width: 40px;
            height: 40px;
            border-radius: 3px;
            margin-right: 10px;
        }
        .moment-item .author {
            font-weight: bold;
            font-size: 16px;
            color: #222;
        }
        .moment-item .content {
            font-size: 16px;
            line-height: 1.5;
            margin: 10px 0;
            color: #222;
            word-wrap: break-word;
            padding: 2px 0;
            text-align: left;
            padding-left: 50px; /* 与头像左边缘对齐，头像宽度40px+右边距10px */
        }
        /* 段落样式 */
        .moment-item .content p {
            margin: 0 0 0.8em 0;
            padding: 0;
        }
        /* 最后一个段落不需要底部边距 */
        .moment-item .content p:last-child {
            margin-bottom: 0;
        }
        /* 特殊标记样式 */
        .moment-item .content .emoji {
            display: inline-block;
            vertical-align: -0.1em;
            width: 1.1em;
            height: 1.1em;
            margin: 0 0.1em;
        }
        .moment-item .content .checkmark {
            color: #07c160;
            font-weight: bold;
        }
        .moment-item .footer {
            display: flex;
            justify-content: space-between;
            color: #999;
            font-size: 13px;
            margin-top: 15px;
            padding-top: 8px;
            border-top: 1px solid #f0f0f0;
        }
        .moment-item .time {
            color: #999;
        }
        .moment-item .actions {
            display: flex;
        }
        .moment-item .action-btn {
            color: #576b95;
            margin-left: 15px;
            text-decoration: none;
            font-weight: 500;
        }
        /* 添加点击反馈效果 */
        .moment-item .action-btn:active {
            opacity: 0.7;
            background-color: rgba(0,0,0,0.05);
            border-radius: 3px;
            padding: 3px;
        }
        .moment-item .images {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;
            padding-left: 50px; /* 与头像左边缘对齐 */
        }
        /* 单张图片样式 */
        .moment-item .images.single-image .img-box {
            width: 66%;
            max-width: 300px;
            margin-right: 0;
            margin-bottom: 0;
            position: relative;
            padding-top: 66%;
        }
        /* 两张图片样式 */
        .moment-item .images.two-images .img-box {
            width: 48.5%;
            margin-right: 3%;
            margin-bottom: 0;
            position: relative;
            padding-top: 48.5%;
        }
        .moment-item .images.two-images .img-box:nth-child(2n) {
            margin-right: 0;
        }
        /* 三张及以上图片样式 */
        .moment-item .img-box {
            width: 31.333%;
            margin-right: 3%;
            margin-bottom: 8px;
            position: relative;
            padding-top: 31.333%;
        }
        .moment-item .img-box:nth-child(3n) {
            margin-right: 0;
        }
        .moment-item .img-box img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 3px;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .modal-img {
            max-width: 90%;
            max-height: 90%;
        }
        /* 固定在左上角的返回按钮 - 白色版本 */
        .fixed-back-btn-white {
            position: fixed;
            top: 12px;
            left: 12px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 998;
            background-color: transparent;
        }
        
        /* 固定在左上角的返回按钮 - 黑色版本 */
        .fixed-back-btn-black {
            position: fixed;
            top: 12px;
            left: 12px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1001; /* 最高层级 */
            background-color: transparent;
            display: none; /* 初始隐藏 */
        }
        
        .back-img {
            width: 24px;
            height: 24px;
        }
        /* 全文链接 */
        .read-more {
            color: #576b95;
            margin-top: 5px;
            display: inline-block;
            margin-left: 50px; /* 与头像左边缘对齐 */
        }
        /* 标题样式 */
        .moment-item .title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #222;
            font-size: 17px;
            padding-left: 50px; /* 与头像左边缘对齐 */
        }
        /* 无换行长文本缩略样式 */
        .content-truncated {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }
    </style>
</head>
<body>
    <!-- 返回按钮 - 白色版本(导航栏未显示时) -->
    <a href="{:U('index/index')}" class="fixed-back-btn-white" id="whiteBackBtn">
        <img src="/static/stations/images/back-w.png" class="back-img" onerror="this.parentNode.style.display='none'">
    </a>
    
    <!-- 返回按钮 - 黑色版本(导航栏显示时) -->
    <a href="{:U('index/index')}" class="fixed-back-btn-black" id="blackBackBtn">
        <img src="/static/stations/images/back.png" class="back-img" onerror="this.parentNode.style.display='none'">
    </a>

    <!-- 微信风格的导航栏 -->
    <div class="nav-header" id="navHeader">
        <div class="nav-left">
            <a href="{:U('index/index')}">
                <i class="iconfont icon-fanhui back-icon"></i>
            </a>
        </div>
        <div class="nav-title">朋友圈素材</div>
        <div class="nav-right">
            <i class="iconfont icon-xiangji camera-icon"></i>
        </div>
    </div>
    
    <div class="content-wrapper">
        <!-- 添加中才国科头部 -->
        <div class="company-header" id="companyHeader">
            <div class="logo-text">中才国科</div>
            <img src="/static/stations/images/logozcgk.png" class="logo-img" onerror="this.parentNode.style.display='none'">
        </div>
        
        <div class="moments-list">
            <php>if (empty($momentsList)) {</php>
                <div class="moment-item">
                    <p style="text-align: center; color: #999;">暂无朋友圈素材</p>
                </div>
            <php>} else {</php>
                <php>foreach($momentsList as $moment) {</php>
                    <div class="moment-item">
                        <div class="header">
                            <img src="{:$moment['avatar']}" class="avatar" onerror="this.src='/static/stations/images/logozcgk.png'">
                            <div class="author">{:$moment['project_name']}</div>
                        </div>
                        <php>
                        // 检查内容的第一行是否可能是标题
                        $contentHtml = $moment['content'];
                        $pattern = '/<p>(.*?)<\/p>/s';
                        $matches = [];
                        preg_match($pattern, $contentHtml, $matches);
                        
                        if(!empty($matches) && strlen($matches[1]) < 50 && strpos($matches[1], '【') !== false && strpos($matches[1], '】') !== false) {
                            // 可能是标题，将其提取出来
                            $title = $matches[1];
                            $contentHtml = preg_replace($pattern, '', $contentHtml, 1);
                            echo '<div class="title">'.$title.'</div>';
                        }
                        </php>
                        <div class="content">{:$contentHtml}</div>
                        <php>if (!empty($moment['image_list'])) {
                            $imageCount = count($moment['image_list']);
                            $imageClass = "";
                            
                            if($imageCount == 1) {
                                $imageClass = "single-image";
                            } else if($imageCount == 2) {
                                $imageClass = "two-images";
                            }
                        </php>
                            <div class="images {:$imageClass}" data-moment-id="{:$moment['id']}">
                                <php>foreach($moment['image_list'] as $key => $image) {</php>
                                    <div class="img-box" id="img-box-{:$image}">
                                        <img src="http://we.zhongcaiguoke.com/{:$image}" 
                                            data-src="http://we.zhongcaiguoke.com/{:$image}" 
                                            data-index="{:$key}" 
                                            class="moment-img" 
                                            onerror="this.parentNode.style.display='none'">
                                    </div>
                                <php>}</php>
                            </div>
                        <php>}</php>
                        <div class="footer">
                            <div class="time">{:$moment['create_time'] ? date('Y-m-d H:i', $moment['create_time']) : '16分钟前'}</div>
                            <div class="actions">
                                <a href="javascript:void(0);" class="action-btn copy-text-btn" onclick="copyThisContent(this)" data-id="{:$moment['id']}">复制文字</a>
                            </div>
                        </div>
                    </div>
                <php>}</php>
            <php>}</php>
        </div>
    </div>

    <!-- 图片预览弹窗 -->
    <div class="modal" style="display: none;" id="imageModal">
        <img class="modal-img" id="modalImage">
    </div>

    <script>
        // 纯原生JavaScript实现，不依赖jQuery
        window.onload = function() {
            // 替换emoji文本为图标
            replaceEmoji();
            
            // 处理长文本
            handleLongText();
            
            // 滚动显示导航栏
            var navHeader = document.getElementById('navHeader');
            var companyHeader = document.getElementById('companyHeader');
            var whiteBackBtn = document.getElementById('whiteBackBtn');
            var blackBackBtn = document.getElementById('blackBackBtn');
            var headerHeight = companyHeader ? companyHeader.offsetHeight : 0;
            
            // 立即检查一次
            checkScroll();
            
            // 滚动事件监听
            window.addEventListener('scroll', checkScroll);
            
            function checkScroll() {
                if (window.pageYOffset > headerHeight) {
                    // 导航栏显示时
                    navHeader.style.display = 'flex';
                    whiteBackBtn.style.display = 'none';
                    blackBackBtn.style.display = 'flex';
                } else {
                    // 导航栏隐藏时
                    navHeader.style.display = 'none';
                    whiteBackBtn.style.display = 'flex';
                    blackBackBtn.style.display = 'none';
                }
            }
            
            // 检测是否在微信环境中
            var isWechat = /MicroMessenger/i.test(navigator.userAgent);
            
            // 配置微信JS-SDK
            if (isWechat && typeof wx !== 'undefined') {
                // 注意：正式环境需要通过后端接口获取config配置
                // 这里假设已经通过其他方式配置好了wx.config
                wx.ready(function() {
                    console.log('微信JS-SDK配置成功');
                });
                
                wx.error(function(res) {
                    console.error('微信JS-SDK配置失败', res);
                });
            }
            
            // 图片预览
            var modal = document.getElementById('imageModal');
            var modalImg = document.getElementById('modalImage');
            var imgs = document.getElementsByClassName('moment-img');
            
            // 收集所有图片URL，按动态组装
            var allImagesGroups = {};
            var imageContainers = document.querySelectorAll('.images');
            
            for (var i = 0; i < imageContainers.length; i++) {
                var container = imageContainers[i];
                var momentId = container.getAttribute('data-moment-id') || 'default';
                var momentImages = container.querySelectorAll('.moment-img');
                
                var urls = [];
                for (var j = 0; j < momentImages.length; j++) {
                    if (momentImages[j].parentNode.style.display !== 'none') {
                        urls.push(momentImages[j].getAttribute('data-src'));
                    }
                }
                
                allImagesGroups[momentId] = urls;
            }
            
            // 为每个图片绑定点击事件
            for (var i = 0; i < imgs.length; i++) {
                imgs[i].onclick = function(e) {
                    e.preventDefault();
                    
                    var src = this.getAttribute('data-src');
                    var index = parseInt(this.getAttribute('data-index')) || 0;
                    var momentId = this.closest('.images').getAttribute('data-moment-id') || 'default';
                    var currentGroup = allImagesGroups[momentId] || [src];
                    
                    if (isWechat && typeof wx !== 'undefined' && wx.previewImage) {
                        // 在微信环境中，使用微信的图片查看工具
                        wx.previewImage({
                            current: src, // 当前显示图片的链接
                            urls: currentGroup // 需要预览的图片链接列表
                        });
                    } else {
                        // 非微信环境，使用自己的预览模态框
                        modal.style.display = "flex";
                        modalImg.src = src;
                    }
                };
            }
            
            modal.onclick = function() {
                modal.style.display = "none";
            };
        };
        
        // 处理长文本，添加阅读更多
        function handleLongText() {
            var contents = document.querySelectorAll('.content');
            
            for(var i = 0; i < contents.length; i++) {
                var content = contents[i];
                
                // 处理无换行的长文本
                var textContent = content.textContent || content.innerText;
                var textLength = textContent.length;
                var hasParagraphs = content.querySelectorAll('p').length > 1;
                var hasLineBreaks = textContent.includes('\n') || content.innerHTML.includes('<br');
                
                // 检查是否是无换行长文本 (超过99个字符且没有明显的段落分隔)
                if(textLength > 99 && !hasParagraphs && !hasLineBreaks) {
                    // 保存原始内容
                    var originalHTML = content.innerHTML;
                    
                    // 应用单行缩略样式
                    content.classList.add('content-truncated');
                    
                    // 添加全文链接
                    var readMoreLink = document.createElement('a');
                    readMoreLink.href = 'javascript:void(0);';
                    readMoreLink.className = 'read-more';
                    readMoreLink.textContent = '全文';
                    readMoreLink.onclick = (function(content, originalHTML) {
                        return function() {
                            if(this.textContent === '全文') {
                                content.classList.remove('content-truncated');
                                this.textContent = '收起';
                            } else {
                                content.classList.add('content-truncated');
                                this.textContent = '全文';
                                // 滚动到内容顶部
                                content.scrollIntoView({behavior: 'smooth'});
                            }
                        };
                    })(content, originalHTML);
                    
                    content.parentNode.insertBefore(readMoreLink, content.nextSibling);
                    
                    // 如果已经应用了单行缩略处理，则跳过后续的多行处理
                    continue;
                }
                
                // 计算行数而不是高度
                var contentStyle = window.getComputedStyle(content);
                var lineHeight = parseFloat(contentStyle.lineHeight);
                
                // 如果lineHeight为normal或auto，使用默认值
                if (isNaN(lineHeight) || lineHeight === 0) {
                    lineHeight = parseFloat(contentStyle.fontSize) * 1.5;
                }
                
                var paragraphs = content.querySelectorAll('p');
                var totalLines = 0;
                
                // 计算所有段落的总行数
                for (var j = 0; j < paragraphs.length; j++) {
                    var paraHeight = paragraphs[j].offsetHeight;
                    totalLines += Math.ceil(paraHeight / lineHeight);
                }
                
                // 如果内容超过5行，才添加"全文"功能
                if(totalLines > 5) {
                    // 保存原始内容
                    var originalHTML = content.innerHTML;
                    
                    // 设置最大高度为5行文字的高度
                    content.style.maxHeight = (5 * lineHeight) + 'px';
                    content.style.overflow = 'hidden';
                    
                    var readMoreLink = document.createElement('a');
                    readMoreLink.href = 'javascript:void(0);';
                    readMoreLink.className = 'read-more';
                    readMoreLink.textContent = '全文';
                    readMoreLink.onclick = (function(content, originalHTML, maxHeight) {
                        return function() {
                            if(this.textContent === '全文') {
                                content.style.maxHeight = 'none';
                                this.textContent = '收起';
                            } else {
                                content.style.maxHeight = maxHeight;
                                this.textContent = '全文';
                                // 滚动到内容顶部
                                content.scrollIntoView({behavior: 'smooth'});
                            }
                        };
                    })(content, originalHTML, (5 * lineHeight) + 'px');
                    
                    content.parentNode.insertBefore(readMoreLink, content.nextSibling);
                }
            }
        }
        
        // 替换常见emoji文本为图标
        function replaceEmoji() {
            var emojiMap = {
                '✅': '<span class="checkmark">✓</span>',
                '👉': '<span class="emoji">👉</span>',
                '🔥': '<span class="emoji">🔥</span>',
                '⭐': '<span class="emoji">⭐</span>',
                '📢': '<span class="emoji">📢</span>',
                '💰': '<span class="emoji">💰</span>',
                '🌟': '<span class="emoji">🌟</span>'
            };
            
            var contents = document.querySelectorAll('.content');
            for(var i = 0; i < contents.length; i++) {
                var html = contents[i].innerHTML;
                
                // 替换emoji
                for(var emoji in emojiMap) {
                    if(emojiMap.hasOwnProperty(emoji)) {
                        var re = new RegExp(emoji, 'g');
                        html = html.replace(re, emojiMap[emoji]);
                    }
                }
                
                contents[i].innerHTML = html;
            }
        }
        
        // 查看内容
        function viewContent(el) {
            var content = el.closest('.moment-item').querySelector('.content').textContent;
            alert('内容详情：\n' + content);
        }
        
        // 直接复制功能 - 使用onclick属性调用
        function copyThisContent(element) {
            var momentItem = element.closest('.moment-item');
            var content = momentItem.querySelector('.content').innerHTML;
            
            // 移除HTML标签获取纯文本
            var tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            content = tempDiv.textContent || tempDiv.innerText || '';
            
            // 恢复正常的换行符
            content = content.replace(/\s*<br\s*\/?>\s*/gi, '\n');
            
            // 添加视觉反馈
            var originalText = element.textContent;
            var originalColor = element.style.color;
            element.style.color = '#4CAF50';
            element.textContent = '复制中...';
            
            // 执行复制
            copyContent(content);
            
            // 复原按钮样式
            setTimeout(function() {
                element.style.color = originalColor;
                element.textContent = originalText;
            }, 1500);
        }
        
        // 复制文字功能 (非微信环境使用)
        function copyContent(text) {
            var textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';  // 避免页面滚动
            textarea.style.opacity = '0';       // 使文本域不可见
            document.body.appendChild(textarea);
            textarea.focus();
            textarea.select();
            
            var successful = false;
            try {
                successful = document.execCommand('copy');
                if (successful) {
                    alert('内容已复制，可以分享给好友');
                } else {
                    alert('复制失败，请尝试长按文字手动复制');
                }
            } catch (err) {
                alert('您的设备不支持复制功能，请长按文字手动复制');
                console.error('复制失败: ', err);
            }
            
            document.body.removeChild(textarea);
        }
    </script>
    

</body>
</html> 